diff --git a/contrib/minizip/iowin32.c b/contrib/minizip/iowin32.c
index 274f39eb1dd2..246ceb91a139 100644
--- a/contrib/minizip/iowin32.c
+++ b/contrib/minizip/iowin32.c
@@ -26,12 +26,19 @@
 #endif
 
 
+#ifdef _WIN32_WINNT
+#undef _WIN32_WINNT
+#define _WIN32_WINNT 0x601
+#endif
+
+#if _WIN32_WINNT >= _WIN32_WINNT_WIN8
 // see Include/shared/winapifamily.h in the Windows Kit
 #if defined(WINAPI_FAMILY_PARTITION) && (!(defined(IOWIN32_USING_WINRT_API)))
 #if WINAPI_FAMILY_ONE_PARTITION(WINAPI_FAMILY, WINAPI_PARTITION_APP)
 #define IOWIN32_USING_WINRT_API 1
 #endif
 #endif
+#endif
 
 voidpf  ZCALLBACK win32_open_file_func  OF((voidpf opaque, const char* filename, int mode));
 uLong   ZCALLBACK win32_read_file_func  OF((voidpf opaque, voidpf stream, void* buf, uLong size));
diff --git a/contrib/minizip/unzip.c b/contrib/minizip/unzip.c
index bcfb9416ec35..199b4723fcfc 100644
--- a/contrib/minizip/unzip.c
+++ b/contrib/minizip/unzip.c
@@ -1705,11 +1705,6 @@ extern int ZEXPORT unzReadCurrentFile  (unzFile file, voidp buf, unsigned len)
 
     pfile_in_zip_read_info->stream.avail_out = (uInt)len;
 
-    if ((len>pfile_in_zip_read_info->rest_read_uncompressed) &&
-        (!(pfile_in_zip_read_info->raw)))
-        pfile_in_zip_read_info->stream.avail_out =
-            (uInt)pfile_in_zip_read_info->rest_read_uncompressed;
-
     if ((len>pfile_in_zip_read_info->rest_read_compressed+
            pfile_in_zip_read_info->stream.avail_in) &&
          (pfile_in_zip_read_info->raw))
diff --git a/gzread.c b/gzread.c
index 956b91ea7d9e..832d3ef98c59 100644
--- a/gzread.c
+++ b/gzread.c
@@ -443,7 +443,11 @@ z_size_t ZEXPORT gzfread(buf, size, nitems, file)
 #  undef z_gzgetc
 #else
 #  undef gzgetc
+#  ifdef Z_CR_PREFIX_SET
+#    define gzgetc Cr_z_gzgetc
+#  endif
 #endif
+
 int ZEXPORT gzgetc(file)
     gzFile file;
 {
diff --git a/zconf.h b/zconf.h
index 5e1d68a004e9..a7a815f575a7 100644
--- a/zconf.h
+++ b/zconf.h
@@ -8,6 +8,10 @@
 #ifndef ZCONF_H
 #define ZCONF_H
 
+/*
+ * This library is also built as a part of AOSP, which does not need to include
+ * chromeconf.h. This config does not want chromeconf.h, so it can set this
+ * macro to opt out. While this works today, there's no guarantee that building
+ * zlib outside of Chromium keeps working in the future.
+ */
+#if !defined(CHROMIUM_ZLIB_NO_CHROMECONF)
+/* This include does prefixing as below, but with an updated set of names.  Also
+ * sets up export macros in component builds. */
+#include "chromeconf.h"
+#endif
+
 /*
  * If you *really* need a unique prefix for all types and library functions,
  * compile with -DZ_PREFIX. The "standard" zlib should be compiled without it.
@@ -431,7 +434,7 @@ typedef uLong FAR uLongf;
    typedef unsigned long z_crc_t;
 #endif
 
-#ifdef HAVE_UNISTD_H    /* may be set to #if 1 by ./configure */
+#if !defined(_WIN32)
 #  define Z_HAVE_UNISTD_H
 #endif
 
diff --git a/zlib.h b/zlib.h
index f09cdaf1e054..99fd467f6b1a 100644
--- a/zlib.h
+++ b/zlib.h
@@ -1824,6 +1824,11 @@ ZEXTERN int ZEXPORT gzgetc_ OF((gzFile file));  /* backward compatibility */
 #  undef z_gzgetc
 #  define z_gzgetc(g) \
           ((g)->have ? ((g)->have--, (g)->pos++, *((g)->next)++) : (gzgetc)(g))
+#elif defined(Z_CR_PREFIX_SET)
+#    undef gzgetc
+#    define gzgetc(g) \
+          ((g)->have ? ((g)->have--, (g)->pos++, *((g)->next)++) \
+                     : (Cr_z_gzgetc)(g))
 #else
 #  define gzgetc(g) \
           ((g)->have ? ((g)->have--, (g)->pos++, *((g)->next)++) : (gzgetc)(g))
@@ -1853,11 +1858,29 @@ ZEXTERN int ZEXPORT gzgetc_ OF((gzFile file));  /* backward compatibility */
 #    define z_adler32_combine z_adler32_combine64
 #    define z_crc32_combine z_crc32_combine64
 #  else
+#    ifdef gzopen
+#      undef gzopen
+#    endif
 #    define gzopen gzopen64
+#    ifdef gzseek
+#      undef gzseek
+#    endif
 #    define gzseek gzseek64
+#    ifdef gztell
+#      undef gztell
+#    endif
 #    define gztell gztell64
+#    ifdef gzoffset
+#      undef gzoffset
+#    endif
 #    define gzoffset gzoffset64
+#    ifdef adler32_combine
+#      undef adler32_combine
+#    endif
 #    define adler32_combine adler32_combine64
+#    ifdef crc32_combine
+#      undef crc32_combine
+#    endif
 #    define crc32_combine crc32_combine64
 #  endif
 #  ifndef Z_LARGE64
diff --git a/zutil.h b/zutil.h
index b079ea6a80f5..80375b8b6109 100644
--- a/zutil.h
+++ b/zutil.h
@@ -28,6 +28,21 @@
 #  include <string.h>
 #  include <stdlib.h>
 #endif
+#ifdef NO_ERRNO_H
+#   ifdef _WIN32_WCE
+      /* The Microsoft C Run-Time Library for Windows CE doesn't have
+       * errno.  We define it as a global variable to simplify porting.
+       * Its value is always 0 and should not be used.  We rename it to
+       * avoid conflict with other libraries that use the same workaround.
+       */
+#     define errno z_errno
+#   endif
+    extern int errno;
+#else
+#  ifndef _WIN32_WCE
+#    include <errno.h>
+#  endif
+#endif
 
 #ifdef Z_SOLO
    typedef long ptrdiff_t;  /* guess -- will be caught if guess is wrong */
