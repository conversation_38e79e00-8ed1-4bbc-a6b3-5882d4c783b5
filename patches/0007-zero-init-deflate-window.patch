From 92537ee19784e0e545f06d89b7d89ab532a18cff Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 3 Nov 2020 15:54:09 +0100
Subject: [PATCH] [zlib] Zero-initialize the window used for deflation

Otherwise MSan complains about use-of-uninitialized values in the
window.
This happens in both regular deflate's longest_match and deflate_rle.

Before crrev.com/822755 we used to suppress those reports, but it seems
better to fix it properly. That will also allow us to catch other
potential issues with MSan in these functions.

The instances of this that we've seen only reproduce with
fill_window_sse(), not with the regular fill_window() function. Since
the former doesn't exist in upstream zlib, I'm not planning to send this
patch upstream.

Bug: 1137613, 1144420
---
 third_party/zlib/deflate.c | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/third_party/zlib/deflate.c b/third_party/zlib/deflate.c
index 8bf93e524875..fc7ae45905ff 100644
--- a/third_party/zlib/deflate.c
+++ b/third_party/zlib/deflate.c
@@ -321,6 +321,9 @@ int ZEXPORT deflateInit2_(strm, level, method, windowBits, memLevel, strategy,
     s->window = (Bytef *) ZALLOC(strm,
                                  s->w_size + window_padding,
                                  2*sizeof(Byte));
+    /* Avoid use of unitialized values in the window, see crbug.com/1137613 and
+     * crbug.com/1144420 */
+    zmemzero(s->window, (s->w_size + window_padding) * (2 * sizeof(Byte)));
     s->prev   = (Posf *)  ZALLOC(strm, s->w_size, sizeof(Pos));
     /* Avoid use of uninitialized value, see:
      * https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11360
-- 
2.29.1.341.ge80a0c044ae-goog

