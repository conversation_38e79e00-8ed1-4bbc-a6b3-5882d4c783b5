From c43ba7a55f091c0dcbfd8d89f7a5121269ce1b81 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 27 Jul 2023 09:47:52 +0800
Subject: [PATCH] [zlib] Perform CPU feature detection for ARM inside adler32()

Perform CPU feature detection for ARM within adler32() to have the same
behavior as x86.

---
 third_party/zlib/adler32.c | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/third_party/zlib/adler32.c b/third_party/zlib/adler32.c
index 81c584f68e233..99a294496f7eb 100644
--- a/third_party/zlib/adler32.c
+++ b/third_party/zlib/adler32.c
@@ -90,9 +90,9 @@ uLong ZEXPORT adler32_z(uLong adler, const Bytef *buf, z_size_t len) {
         return adler | (sum2 << 16);
     }

-#if defined(ADLER32_SIMD_SSSE3)
+#if defined(ADLER32_SIMD_SSSE3) || defined(ADLER32_SIMD_NEON)
     /*
-     * Use SSSE3 to compute the adler32. Since this routine can be
+     * Use SIMD to compute the adler32. Since this function can be
      * freely used, check CPU features here. zlib convention is to
      * call adler32(0, NULL, 0), before making calls to adler32().
      * So this is a good early (and infrequent) place to cache CPU
--
2.41.0.windows.3
