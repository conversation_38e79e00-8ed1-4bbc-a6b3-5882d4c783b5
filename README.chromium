Name: zlib
Short Name: zlib
URL: http://zlib.net/
Version: 1.2.13
CPEPrefix: cpe:/a:zlib:zlib:1.2.13
Security Critical: yes
Shipped: yes
License: Custom license
License File: LICENSE
License Android Compatible: yes

Description:
"A massively spiffy yet delicately unobtrusive compression library."

zlib is a free, general-purpose, legally unencumbered lossless data-compression
library. zlib implements the "deflate" compression algorithm described by RFC
1951, which combines the LZ77 (Lempel-Ziv) algorithm with Hu<PERSON>man coding. zlib
also implements the zlib (RFC 1950) and gzip (RFC 1952) wrapper formats.

Local Modifications:
 - Only source code from the zlib distribution used to build the zlib and
   minizip libraries are present. Many other files have been omitted. Only *.c
   and *.h files from the upstream root directory and contrib/minizip were
   imported.
 - The contents of the google directory are original Chromium-specific
   additions.
 - Added chromeconf.h
 - Plus the changes in 'patches' folder.
 - Code in contrib/ other than contrib/minizip was added to match zlib's
   contributor layout.
 - In sync with 1.2.13 official release
 - ZIP reader modified to allow for progress callbacks during extraction.
 - ZIP reader modified to add detection of AES encrypted content.
