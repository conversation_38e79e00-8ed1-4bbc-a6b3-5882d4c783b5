# Copyright 2013 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/compiler/compiler.gni")

declare_args() {
  # Expose zlib's symbols, used by Node.js to provide zlib APIs for its native
  # modules.
  zlib_symbols_visible = false
}

if (build_with_chromium) {
  import("//testing/test.gni")
}

if (current_cpu == "arm" || current_cpu == "arm64") {
  import("//build/config/arm.gni")
}

config("zlib_config") {
  include_dirs = [ "." ]

  if (zlib_symbols_visible) {
    defines = [ "ZLIB_DLL" ]
  }
}

config("zlib_internal_config") {
  defines = [ "ZLIB_IMPLEMENTATION" ]

  if (!is_debug) {
    # Build code using -O3, see: crbug.com/1084371.
    configs = [ "//build/config/compiler:optimize_speed" ]
  }
  if (is_debug || use_fuzzing_engine) {
    # Enable zlib's asserts in debug and fuzzer builds.
    defines += [ "ZLIB_DEBUG" ]
  }

  if (is_win && !is_clang) {
    # V8 supports building with msvc, these silence some warnings that
    # causes compilation to fail (https://crbug.com/1255096).
    cflags = [
      "/wd4244",
      "/wd4100",
      "/wd4702",
      "/wd4127",
    ]
  }
}

source_set("zlib_common_headers") {
  visibility = [ ":*" ]

  sources = [
    "chromeconf.h",
    "deflate.h",
    "inffast.h",
    "inffixed.h",
    "inflate.h",
    "inftrees.h",
    "zconf.h",
    "zlib.h",
    "zutil.h",
  ]
}

use_arm_neon_optimizations = false
if ((current_cpu == "arm" || current_cpu == "arm64") &&
    !(is_win && !is_clang)) {
  # TODO(<EMAIL>): Optimizations temporarily disabled for
  # Windows on Arm MSVC builds, see http://crbug.com/v8/10012.
  if (arm_use_neon) {
    use_arm_neon_optimizations = true
  }
}

use_x86_x64_optimizations =
    (current_cpu == "x86" || current_cpu == "x64") && !is_ios && !is_tvos && !is_android

config("zlib_adler32_simd_config") {
  if (use_x86_x64_optimizations) {
    defines = [ "ADLER32_SIMD_SSSE3" ]
    if (is_win) {
      defines += [ "X86_WINDOWS" ]
    } else {
      defines += [ "X86_NOT_WINDOWS" ]
    }
  }

  if (use_arm_neon_optimizations) {
    defines = [ "ADLER32_SIMD_NEON" ]
  }
}

source_set("zlib_adler32_simd") {
  visibility = [ ":*" ]

  if (use_x86_x64_optimizations) {
    sources = [
      "adler32_simd.c",
      "adler32_simd.h",
    ]

    if (!is_win || is_clang) {
      cflags = [ "-mssse3" ]
    }
  }

  if (use_arm_neon_optimizations) {
    sources = [
      "adler32_simd.c",
      "adler32_simd.h",
    ]
  }

  configs += [ ":zlib_internal_config" ]

  public_configs = [ ":zlib_adler32_simd_config" ]

  public_deps = [ ":zlib_common_headers" ]
}

if (use_arm_neon_optimizations) {
  config("zlib_arm_crc32_config") {
    defines = [ "CRC32_ARMV8_CRC32" ]
    if (is_android) {
      defines += [ "ARMV8_OS_ANDROID" ]
    } else if (is_linux || is_chromeos) {
      defines += [ "ARMV8_OS_LINUX" ]
    } else if (is_mac) {
      defines += [ "ARMV8_OS_MACOS" ]
    } else if (is_ios || is_tvos) {
      defines += [ "ARMV8_OS_IOS" ]
    } else if (is_fuchsia) {
      defines += [ "ARMV8_OS_FUCHSIA" ]
    } else if (is_win) {
      defines += [ "ARMV8_OS_WINDOWS" ]
    } else {
      assert(false, "Unsupported ARM OS")
    }
  }

  source_set("zlib_arm_crc32") {
    visibility = [ ":*" ]

    include_dirs = [ "." ]

    if (!is_win && !is_clang) {
      assert(!use_thin_lto,
             "ThinLTO fails mixing different module-level targets")
      cflags_c = [ "-march=armv8-a+aes+crc" ]
    }

    sources = [
      "crc32_simd.c",
      "crc32_simd.h",
    ]

    configs += [ ":zlib_internal_config" ]

    public_configs = [ ":zlib_arm_crc32_config" ]

    public_deps = [ ":zlib_common_headers" ]
  }
}

config("zlib_inflate_chunk_simd_config") {
  if (use_x86_x64_optimizations) {
    defines = [ "INFLATE_CHUNK_SIMD_SSE2" ]

    if (current_cpu == "x64") {
      defines += [ "INFLATE_CHUNK_READ_64LE" ]
    }
  }

  if (use_arm_neon_optimizations) {
    defines = [ "INFLATE_CHUNK_SIMD_NEON" ]

    if (current_cpu == "arm64") {
      defines += [ "INFLATE_CHUNK_READ_64LE" ]
    }
  }
}

source_set("zlib_inflate_chunk_simd") {
  visibility = [ ":*" ]

  if (use_x86_x64_optimizations || use_arm_neon_optimizations) {
    include_dirs = [ "." ]

    sources = [
      "contrib/optimizations/chunkcopy.h",
      "contrib/optimizations/inffast_chunk.c",
      "contrib/optimizations/inffast_chunk.h",
      "contrib/optimizations/inflate.c",
    ]
  }

  configs += [ ":zlib_internal_config" ]

  # Needed for MSVC, which is still supported by V8 and PDFium. zlib uses K&R C
  # style function declarations, which triggers warning C4131.
  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]
  configs += [ ":zlib_warnings" ]

  public_configs = [ ":zlib_inflate_chunk_simd_config" ]

  public_deps = [ ":zlib_common_headers" ]
}

config("zlib_crc32_simd_config") {
  if (use_x86_x64_optimizations) {
    defines = [ "CRC32_SIMD_SSE42_PCLMUL" ]
  }
}

source_set("zlib_crc32_simd") {
  visibility = [ ":*" ]

  if (use_x86_x64_optimizations) {
    sources = [
      "crc32_simd.c",
      "crc32_simd.h",
      "crc_folding.c",
    ]

    if (!is_win || is_clang) {
      cflags = [
        "-msse4.2",
        "-mpclmul",
      ]
    }
  }

  configs += [ ":zlib_internal_config" ]

  public_configs = [ ":zlib_crc32_simd_config" ]

  public_deps = [ ":zlib_common_headers" ]
}

config("zlib_slide_hash_simd_config") {
  if (use_x86_x64_optimizations) {
    defines = [ "DEFLATE_SLIDE_HASH_SSE2" ]
  }

  if (use_arm_neon_optimizations) {
    defines = [ "DEFLATE_SLIDE_HASH_NEON" ]
  }
}

source_set("zlib_slide_hash_simd") {
  visibility = [ ":*" ]

  if (use_x86_x64_optimizations) {
    sources = [ "slide_hash_simd.h" ]
  }

  if (use_arm_neon_optimizations) {
    sources = [ "slide_hash_simd.h" ]
  }

  configs += [ ":zlib_internal_config" ]

  public_configs = [ ":zlib_slide_hash_simd_config" ]

  public_deps = [ ":zlib_common_headers" ]
}

config("zlib_warnings") {
  if (is_clang) {
    cflags = [
      "-Wno-unknown-warning-option",
      "-Wno-deprecated-non-prototype",
      "-Wno-incompatible-pointer-types",
      "-Wunused-variable",
    ]
  }
}

component("zlib") {
  if (!is_win) {
    # Don't stomp on "libzlib" on other platforms.
    output_name = "chrome_zlib"
  }

  sources = [
    "adler32.c",
    "chromeconf.h",
    "compress.c",
    "contrib/optimizations/insert_string.h",
    "cpu_features.c",
    "cpu_features.h",
    "crc32.c",
    "crc32.h",
    "deflate.c",
    "deflate.h",
    "gzclose.c",
    "gzguts.h",
    "gzlib.c",
    "gzread.c",
    "gzwrite.c",
    "infback.c",
    "inffast.c",
    "inffast.h",
    "inffixed.h",
    "inflate.h",
    "inftrees.c",
    "inftrees.h",
    "trees.c",
    "trees.h",
    "uncompr.c",
    "zconf.h",
    "zlib.h",
    "zutil.c",
    "zutil.h",
  ]

  defines = []
  deps = []

  if (!use_x86_x64_optimizations && !use_arm_neon_optimizations) {
    # Apparently android_cronet bot builds with NEON disabled and
    # we also should disable optimizations for iOS@x86 (a.k.a. simulator).
    defines += [ "CPU_NO_SIMD" ]
  }

  if (use_x86_x64_optimizations || use_arm_neon_optimizations) {
    deps += [
      ":zlib_adler32_simd",
      ":zlib_inflate_chunk_simd",
      ":zlib_slide_hash_simd",
    ]

    if (use_x86_x64_optimizations) {
      deps += [ ":zlib_crc32_simd" ]
    } else if (use_arm_neon_optimizations) {
      deps += [ ":zlib_arm_crc32" ]
    }
  } else {
    sources += [ "inflate.c" ]
  }

  if (is_android) {
    import("//build/config/android/config.gni")
    if (defined(android_ndk_root) && android_ndk_root != "") {
      deps += [ "//third_party/cpu-features" ]
    } else {
      assert(false, "CPU detection requires the Android NDK")
    }
  }

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]

  if (zlib_symbols_visible) {
    configs -= [ "//build/config/gcc:symbol_visibility_hidden" ]
    configs += [ "//build/config/gcc:symbol_visibility_default" ]
  }

  public_configs = [ ":zlib_config" ]

  configs += [
    ":zlib_internal_config",

    # Must be after no_chromium_code for warning flags to be ordered correctly.
    ":zlib_warnings",
  ]

  allow_circular_includes_from = deps
}

config("minizip_warnings") {
  visibility = [ ":*" ]

  if (is_clang) {
    cflags = [
      # zlib uses `if ((a == b))` for some reason.
      "-Wno-parentheses-equality",
      "-Wno-deprecated-non-prototype",
    ]
  }
}

static_library("minizip") {
  include_dirs = [
    ".",
    "//lynx/third_party/zlib",
  ]

  sources = [
    "contrib/minizip/ioapi.c",
    "contrib/minizip/ioapi.h",
    "contrib/minizip/iowin32.c",
    "contrib/minizip/iowin32.h",
    "contrib/minizip/unzip.c",
    "contrib/minizip/unzip.h",
    "contrib/minizip/zip.c",
    "contrib/minizip/zip.h",
  ]

  if (!is_win) {
    sources -= [
      "contrib/minizip/iowin32.c",
      "contrib/minizip/iowin32.h",
    ]
  }

  if (is_apple || is_android || is_nacl) {
    # Mac, Android and the BSDs don't have fopen64, ftello64, or fseeko64. We
    # use fopen, ftell, and fseek instead on these systems.
    defines = [ "USE_FILE32API" ]
  }

  deps = [ ":zlib" ]

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]

  public_configs = [ ":zlib_config" ]

  configs += [
    # Must be after no_chromium_code for warning flags to be ordered correctly.
    ":minizip_warnings",
  ]
}

executable("zlib_bench") {
  include_dirs = [ "." ]

  sources = [ "contrib/bench/zlib_bench.cc" ]
  if (!is_debug) {
    configs -= [ "//build/config/compiler:default_optimization" ]
    configs += [ "//build/config/compiler:optimize_speed" ]
  }

  deps = [ ":zlib" ]

  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [ "//build/config/compiler:no_chromium_code" ]
}

if (!is_win || target_os != "winuwp") {
  executable("minizip_bin") {
    include_dirs = [ "." ]

    sources = [ "contrib/minizip/minizip.c" ]

    if (is_clang) {
      cflags = [
        "-Wno-incompatible-pointer-types-discards-qualifiers",

        "-Wno-deprecated-non-prototype",
      ]
    }

    if (!is_debug) {
      configs -= [ "//build/config/compiler:default_optimization" ]
      configs += [ "//build/config/compiler:optimize_speed" ]
    }

    deps = [ ":minizip" ]

    configs -= [ "//build/config/compiler:chromium_code" ]
    configs += [ "//build/config/compiler:no_chromium_code" ]
  }

  executable("miniunz_bin") {
    include_dirs = [ "." ]

    sources = [ "contrib/minizip/miniunz.c" ]

    if (is_clang) {
      cflags = [
        "-Wno-incompatible-pointer-types-discards-qualifiers",
        "-Wno-deprecated-non-prototype",
      ]
    }

    if (!is_debug) {
      configs -= [ "//build/config/compiler:default_optimization" ]
      configs += [ "//build/config/compiler:optimize_speed" ]
    }

    deps = [ ":minizip" ]

    configs -= [ "//build/config/compiler:chromium_code" ]
    configs += [ "//build/config/compiler:no_chromium_code" ]
  }
}

if (build_with_chromium) {
  test("zlib_unittests") {
    testonly = true

    sources = [
      "contrib/tests/infcover.cc",
      "contrib/tests/infcover.h",
      "contrib/tests/run_all_unittests.cc",
      "contrib/tests/utils_unittest.cc",
      "google/compression_utils_unittest.cc",
      "google/zip_reader_unittest.cc",
      "google/zip_unittest.cc",
    ]

    data = [ "google/test/data/" ]

    if (is_ios) {
      bundle_deps = [ "google:zlib_pak_bundle_data" ]
    }

    deps = [
      ":minizip",
      ":zlib",
      "google:compression_utils",
      "google:zip",
      "//base/test:test_support",
      "//testing/gtest",
    ]

    configs -= [ "//build/config/compiler:chromium_code" ]
    configs += [ "//build/config/compiler:no_chromium_code" ]

    include_dirs = [
      "//lynx/third_party/googletest/src/googletest/include/gtest",
      ".",
      "google",
    ]
  }
}
